const mongoose = require('mongoose');

/**
 * Content Management Schema
 * Handles blog posts, pages, and other content with version control and approval workflow
 */

const contentSchema = new mongoose.Schema({
  // Basic Content Information
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: /^[a-z0-9-]+$/
  },
  content: {
    type: String,
    required: true
  },
  excerpt: {
    type: String,
    maxlength: 500
  },
  
  // Content Type and Category
  type: {
    type: String,
    enum: ['post', 'page', 'profile_section', 'media', 'template'],
    default: 'post',
    required: true
  },
  category: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],

  // Publishing Information
  status: {
    type: String,
    enum: ['draft', 'pending_review', 'approved', 'published', 'archived', 'rejected'],
    default: 'draft',
    required: true
  },
  publishedAt: Date,
  scheduledFor: Date,
  
  // SEO and Metadata
  seo: {
    metaTitle: {
      type: String,
      maxlength: 60
    },
    metaDescription: {
      type: String,
      maxlength: 160
    },
    keywords: [{
      type: String,
      trim: true
    }],
    canonicalUrl: String,
    ogImage: String,
    ogTitle: String,
    ogDescription: String
  },

  // Media and Assets
  featuredImage: {
    url: String,
    alt: String,
    caption: String
  },
  gallery: [{
    url: String,
    alt: String,
    caption: String,
    order: Number
  }],
  attachments: [{
    filename: String,
    originalName: String,
    url: String,
    mimeType: String,
    size: Number,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Author and Permissions
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  contributors: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['editor', 'reviewer', 'contributor'],
      default: 'contributor'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Workflow and Approval
  workflow: {
    currentStep: {
      type: String,
      enum: ['draft', 'review', 'approval', 'published'],
      default: 'draft'
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    dueDate: Date,
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium'
    }
  },

  // Version Control
  version: {
    type: Number,
    default: 1
  },
  versions: [{
    version: Number,
    title: String,
    content: String,
    excerpt: String,
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    changeLog: String,
    status: String
  }],

  // Comments and Reviews
  comments: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: 1000
    },
    type: {
      type: String,
      enum: ['comment', 'review', 'approval', 'rejection'],
      default: 'comment'
    },
    isInternal: {
      type: Boolean,
      default: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: Date
  }],

  // Analytics and Performance
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    uniqueViews: {
      type: Number,
      default: 0
    },
    lastViewed: Date,
    avgTimeOnPage: Number,
    bounceRate: Number,
    socialShares: {
      facebook: { type: Number, default: 0 },
      twitter: { type: Number, default: 0 },
      linkedin: { type: Number, default: 0 },
      other: { type: Number, default: 0 }
    }
  },

  // Content Settings
  settings: {
    allowComments: {
      type: Boolean,
      default: true
    },
    requireApproval: {
      type: Boolean,
      default: false
    },
    isSticky: {
      type: Boolean,
      default: false
    },
    isFeatured: {
      type: Boolean,
      default: false
    },
    visibility: {
      type: String,
      enum: ['public', 'private', 'password_protected', 'members_only'],
      default: 'public'
    },
    password: String // For password-protected content
  },

  // Audit Trail
  audit: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    publishedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    archivedAt: Date,
    archivedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
contentSchema.index({ slug: 1 }, { unique: true });
contentSchema.index({ status: 1, publishedAt: -1 });
contentSchema.index({ type: 1, status: 1 });
contentSchema.index({ author: 1, createdAt: -1 });
contentSchema.index({ tags: 1 });
contentSchema.index({ category: 1, status: 1 });
contentSchema.index({ 'workflow.assignedTo': 1, 'workflow.currentStep': 1 });
contentSchema.index({ scheduledFor: 1, status: 1 });

// Text search index
contentSchema.index({
  title: 'text',
  content: 'text',
  excerpt: 'text',
  tags: 'text'
});

// Virtual for URL
contentSchema.virtual('url').get(function() {
  return `/${this.type}/${this.slug}`;
});

// Virtual for reading time (approximate)
contentSchema.virtual('readingTime').get(function() {
  const wordsPerMinute = 200;
  const wordCount = this.content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
});

// Pre-save middleware
contentSchema.pre('save', function(next) {
  // Update version if content changed
  if (this.isModified('content') || this.isModified('title')) {
    if (!this.isNew) {
      // Save current version to history
      this.versions.push({
        version: this.version,
        title: this.title,
        content: this.content,
        excerpt: this.excerpt,
        author: this.audit.updatedBy || this.author,
        createdAt: new Date(),
        status: this.status
      });
      
      this.version += 1;
    }
  }

  // Auto-generate excerpt if not provided
  if (!this.excerpt && this.content) {
    this.excerpt = this.content.substring(0, 200).replace(/<[^>]*>/g, '') + '...';
  }

  // Update audit trail
  if (this.isModified() && !this.isNew) {
    this.audit.updatedAt = new Date();
  }

  next();
});

// Static methods
contentSchema.statics.findPublished = function() {
  return this.find({
    status: 'published',
    publishedAt: { $lte: new Date() }
  }).sort({ publishedAt: -1 });
};

contentSchema.statics.findBySlug = function(slug) {
  return this.findOne({ slug, status: 'published' });
};

contentSchema.statics.findPendingReview = function() {
  return this.find({
    status: 'pending_review'
  }).populate('author', 'username email')
    .populate('workflow.assignedTo', 'username email')
    .sort({ createdAt: -1 });
};

contentSchema.statics.findScheduled = function() {
  return this.find({
    status: 'approved',
    scheduledFor: { $lte: new Date() }
  });
};

// Instance methods
contentSchema.methods.publish = function(publishedBy) {
  this.status = 'published';
  this.publishedAt = new Date();
  this.audit.publishedBy = publishedBy;
  this.workflow.currentStep = 'published';
  return this.save();
};

contentSchema.methods.archive = function(archivedBy) {
  this.status = 'archived';
  this.audit.archivedAt = new Date();
  this.audit.archivedBy = archivedBy;
  return this.save();
};

contentSchema.methods.addComment = function(authorId, content, type = 'comment', isInternal = true) {
  this.comments.push({
    author: authorId,
    content,
    type,
    isInternal,
    createdAt: new Date()
  });
  return this.save();
};

contentSchema.methods.incrementViews = function(isUnique = false) {
  this.analytics.views += 1;
  if (isUnique) {
    this.analytics.uniqueViews += 1;
  }
  this.analytics.lastViewed = new Date();
  return this.save();
};

contentSchema.methods.canEdit = function(user) {
  // Author can always edit
  if (this.author.toString() === user._id.toString()) {
    return true;
  }

  // Check if user is a contributor
  const contributor = this.contributors.find(c => 
    c.user.toString() === user._id.toString()
  );
  
  if (contributor && contributor.role === 'editor') {
    return true;
  }

  // Check user permissions
  return user.hasPermission('content', 'update') || user.role === 'admin' || user.role === 'super_admin';
};

contentSchema.methods.canPublish = function(user) {
  return user.hasPermission('content', 'publish') || user.role === 'admin' || user.role === 'super_admin';
};

module.exports = mongoose.model('Content', contentSchema);
