const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const path = require('path');
require('dotenv').config();

// Import security middleware
const {
  generalRateLimit,
  securityHeaders,
  sanitizeInput,
  requestSizeLimit,
  suspiciousActivityDetection,
  csrfProtection,
  csrfErrorHandler,
  hppProtection,
  requestLogger,
  errorHandler
} = require('./middleware/security');

// Import configuration
const { securityConfig, validateSecurityConfig } = require('./config/security');
const { logger } = require('./utils/logger');

// Import routes
const authRoutes = require('./routes/auth');
const adminRoutes = require('./routes/admin');
const contentRoutes = require('./routes/content');

/**
 * Fortune 500 Admin Panel Server
 * Enterprise-grade security and performance
 */

class AdminServer {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    
    this.init();
  }

  async init() {
    try {
      // Validate security configuration
      validateSecurityConfig();
      
      // Connect to database
      await this.connectDatabase();
      
      // Setup middleware
      this.setupMiddleware();
      
      // Setup routes
      this.setupRoutes();
      
      // Setup error handling
      this.setupErrorHandling();
      
      // Start server
      this.startServer();
      
    } catch (error) {
      logger.error('Server initialization failed', { error: error.message });
      process.exit(1);
    }
  }

  async connectDatabase() {
    try {
      const mongoOptions = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferMaxEntries: 0,
        bufferCommands: false,
      };

      await mongoose.connect(process.env.MONGODB_URI, mongoOptions);
      
      logger.info('Database connected successfully', {
        database: mongoose.connection.name,
        host: mongoose.connection.host,
        port: mongoose.connection.port
      });

      // Database event listeners
      mongoose.connection.on('error', (error) => {
        logger.error('Database error', { error: error.message });
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('Database disconnected');
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('Database reconnected');
      });

    } catch (error) {
      logger.error('Database connection failed', { error: error.message });
      throw error;
    }
  }

  setupMiddleware() {
    // Trust proxy (for accurate IP addresses behind load balancers)
    this.app.set('trust proxy', 1);

    // Security headers (must be first)
    this.app.use(securityHeaders);

    // Request logging
    this.app.use(requestLogger);

    // Compression
    this.app.use(compression({
      level: 6,
      threshold: 1024,
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      }
    }));

    // CORS
    this.app.use(cors(securityConfig.cors));

    // Request size limiting
    this.app.use(requestSizeLimit);

    // Body parsing
    this.app.use(express.json({ 
      limit: '10mb',
      verify: (req, res, buf) => {
        // Store raw body for webhook verification if needed
        req.rawBody = buf;
      }
    }));
    this.app.use(express.urlencoded({ 
      extended: true, 
      limit: '10mb' 
    }));

    // Cookie parsing
    this.app.use(cookieParser());

    // HTTP Parameter Pollution protection
    this.app.use(hppProtection);

    // Input sanitization
    this.app.use(sanitizeInput);

    // Suspicious activity detection
    this.app.use(suspiciousActivityDetection);

    // Rate limiting
    this.app.use(generalRateLimit);

    // Static files (admin panel)
    this.app.use('/admin', express.static(path.join(__dirname, 'public/admin'), {
      maxAge: process.env.NODE_ENV === 'production' ? '1d' : 0,
      etag: true,
      lastModified: true,
      setHeaders: (res, path) => {
        // Security headers for static files
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        
        // Cache control for different file types
        if (path.endsWith('.html')) {
          res.setHeader('Cache-Control', 'no-cache');
        } else if (path.match(/\.(js|css)$/)) {
          res.setHeader('Cache-Control', 'public, max-age=31536000');
        }
      }
    }));

    // CSRF protection (after static files)
    this.app.use('/api', csrfProtection);
  }

  setupRoutes() {
    // Health check endpoint (no auth required)
    this.app.get('/health', (req, res) => {
      const healthCheck = {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.APP_VERSION || '1.0.0',
        database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
        memory: process.memoryUsage(),
        pid: process.pid
      };

      res.status(200).json(healthCheck);
    });

    // CSRF token endpoint
    this.app.get('/api/csrf-token', (req, res) => {
      res.json({ csrfToken: req.csrfToken() });
    });

    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/admin', adminRoutes);
    this.app.use('/api/content', contentRoutes);

    // Admin panel route (serve index.html for SPA)
    this.app.get('/admin/*', (req, res) => {
      res.sendFile(path.join(__dirname, 'public/admin/index.html'));
    });

    // Root redirect to admin
    this.app.get('/', (req, res) => {
      res.redirect('/admin');
    });

    // API 404 handler
    this.app.use('/api/*', (req, res) => {
      logger.warn('API endpoint not found', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.status(404).json({
        error: 'API endpoint not found',
        code: 'ENDPOINT_NOT_FOUND'
      });
    });

    // Catch-all for SPA routing
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, 'public/admin/index.html'));
    });
  }

  setupErrorHandling() {
    // CSRF error handler
    this.app.use(csrfErrorHandler);

    // Global error handler
    this.app.use(errorHandler);

    // Unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Promise Rejection', {
        reason: reason.toString(),
        stack: reason.stack,
        promise: promise.toString()
      });
    });

    // Uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack
      });
      
      // Graceful shutdown
      this.gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    // Graceful shutdown signals
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
  }

  startServer() {
    this.server = this.app.listen(this.port, () => {
      logger.info('Admin server started', {
        port: this.port,
        environment: process.env.NODE_ENV,
        pid: process.pid,
        nodeVersion: process.version,
        platform: process.platform
      });

      // Log security configuration status
      logger.info('Security configuration loaded', {
        jwtEnabled: !!process.env.JWT_SECRET,
        sessionEnabled: !!process.env.SESSION_SECRET,
        encryptionEnabled: !!process.env.ENCRYPTION_KEY,
        corsEnabled: true,
        rateLimitEnabled: true,
        csrfEnabled: true
      });
    });

    // Server error handling
    this.server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${this.port} is already in use`);
      } else {
        logger.error('Server error', { error: error.message });
      }
      process.exit(1);
    });

    // Handle server timeout
    this.server.timeout = 30000; // 30 seconds
    this.server.keepAliveTimeout = 65000; // 65 seconds
    this.server.headersTimeout = 66000; // 66 seconds
  }

  async gracefulShutdown(signal) {
    logger.info(`Received ${signal}, starting graceful shutdown`);

    // Stop accepting new connections
    this.server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // Close database connection
        await mongoose.connection.close();
        logger.info('Database connection closed');

        // Exit process
        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown', { error: error.message });
        process.exit(1);
      }
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);
  }
}

// Start the server
if (require.main === module) {
  new AdminServer();
}

module.exports = AdminServer;
