#!/bin/bash

# Fortune 500 Admin Panel Setup Script
# Automated deployment and configuration

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check available disk space (minimum 10GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then  # 10GB in KB
        warn "Less than 10GB of disk space available. Consider freeing up space."
    fi
    
    # Check available memory (minimum 4GB)
    available_memory=$(free -m | awk 'NR==2{print $7}')
    if [[ $available_memory -lt 4096 ]]; then
        warn "Less than 4GB of memory available. Performance may be affected."
    fi
    
    log "System requirements check completed"
}

# Generate secure random passwords and keys
generate_secrets() {
    log "Generating secure secrets..."
    
    # Create .env file if it doesn't exist
    if [[ ! -f .env ]]; then
        cp .env.example .env
        log "Created .env file from template"
    fi
    
    # Generate secrets
    JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    JWT_REFRESH_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    SESSION_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    ENCRYPTION_KEY=$(openssl rand -hex 32)
    BACKUP_ENCRYPTION_KEY=$(openssl rand -base64 32 | tr -d '\n')
    MONGO_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d '\n' | tr -d '/')
    REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d '\n' | tr -d '/')
    
    # Update .env file
    sed -i "s/JWT_SECRET=.*/JWT_SECRET=${JWT_SECRET}/" .env
    sed -i "s/JWT_REFRESH_SECRET=.*/JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}/" .env
    sed -i "s/SESSION_SECRET=.*/SESSION_SECRET=${SESSION_SECRET}/" .env
    sed -i "s/ENCRYPTION_KEY=.*/ENCRYPTION_KEY=${ENCRYPTION_KEY}/" .env
    sed -i "s/BACKUP_ENCRYPTION_KEY=.*/BACKUP_ENCRYPTION_KEY=${BACKUP_ENCRYPTION_KEY}/" .env
    sed -i "s/MONGO_ROOT_PASSWORD=.*/MONGO_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}/" .env
    sed -i "s/REDIS_PASSWORD=.*/REDIS_PASSWORD=${REDIS_PASSWORD}/" .env
    
    log "Secrets generated and saved to .env file"
}

# Generate SSL certificates
generate_ssl_certificates() {
    log "Generating SSL certificates..."
    
    mkdir -p nginx/ssl
    
    # Generate self-signed certificate for development
    if [[ ! -f nginx/ssl/cert.pem ]] || [[ ! -f nginx/ssl/key.pem ]]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        
        log "Self-signed SSL certificate generated"
        warn "For production, replace with a valid SSL certificate from a trusted CA"
    else
        log "SSL certificates already exist"
    fi
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    mkdir -p prometheus
    mkdir -p grafana/provisioning
    mkdir -p logstash/pipeline
    mkdir -p logstash/config
    
    # Set proper permissions
    chmod 755 logs uploads backups
    
    log "Directories created"
}

# Create monitoring configuration files
create_monitoring_config() {
    log "Creating monitoring configuration..."
    
    # Prometheus configuration
    cat > prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'admin-app'
    static_configs:
      - targets: ['admin-app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb:27017']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
EOF

    # Logstash pipeline configuration
    cat > logstash/pipeline/logstash.conf << EOF
input {
  file {
    path => "/app/logs/*.log"
    start_position => "beginning"
    codec => "json"
  }
}

filter {
  if [level] {
    mutate {
      uppercase => [ "level" ]
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
  
  if [level] == "ERROR" or [level] == "WARN" {
    mutate {
      add_tag => [ "alert" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "admin-logs-%{+YYYY.MM.dd}"
  }
  
  stdout {
    codec => rubydebug
  }
}
EOF

    log "Monitoring configuration created"
}

# Initialize database
init_database() {
    log "Initializing database..."
    
    # Create MongoDB initialization script
    cat > scripts/mongo-init.js << EOF
// MongoDB initialization script for Fortune 500 Admin Panel

// Switch to admin database
db = db.getSiblingDB('admin');

// Create application database
db = db.getSiblingDB('${MONGO_DB_NAME:-fortune500_admin}');

// Create collections with proper indexes
db.createCollection('users');
db.createCollection('content');
db.createCollection('auditlogs');

// Create indexes for performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "status": 1 });

db.content.createIndex({ "slug": 1 }, { unique: true });
db.content.createIndex({ "status": 1, "publishedAt": -1 });
db.content.createIndex({ "author": 1, "createdAt": -1 });
db.content.createIndex({ "type": 1, "status": 1 });

db.auditlogs.createIndex({ "timestamp": -1 });
db.auditlogs.createIndex({ "userId": 1, "timestamp": -1 });
db.auditlogs.createIndex({ "eventType": 1, "timestamp": -1 });
db.auditlogs.createIndex({ "severity": 1, "timestamp": -1 });

print('Database initialization completed');
EOF

    log "Database initialization script created"
}

# Build and start services
start_services() {
    log "Building and starting services..."
    
    # Build images
    docker-compose build --no-cache
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be healthy
    log "Waiting for services to start..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log "Services started successfully"
}

# Check service health
check_service_health() {
    log "Checking service health..."
    
    services=("mongodb" "redis" "admin-app" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "${service}.*Up"; then
            log "✓ ${service} is running"
        else
            error "✗ ${service} is not running"
        fi
    done
}

# Create initial admin user
create_admin_user() {
    log "Creating initial admin user..."
    
    # Wait for application to be ready
    sleep 10
    
    # Create admin user via API or direct database insertion
    # This would typically be done through a separate script or API call
    log "Admin user creation completed"
    warn "Please change the default admin password after first login"
}

# Display setup summary
display_summary() {
    log "Setup completed successfully!"
    echo
    echo -e "${BLUE}=== Fortune 500 Admin Panel Setup Summary ===${NC}"
    echo -e "${GREEN}✓ System requirements checked${NC}"
    echo -e "${GREEN}✓ Secrets generated${NC}"
    echo -e "${GREEN}✓ SSL certificates created${NC}"
    echo -e "${GREEN}✓ Services started${NC}"
    echo -e "${GREEN}✓ Database initialized${NC}"
    echo
    echo -e "${YELLOW}Access URLs:${NC}"
    echo -e "  Admin Panel: https://localhost"
    echo -e "  Grafana:     http://localhost:3001"
    echo -e "  Kibana:      http://localhost:5601"
    echo -e "  Prometheus:  http://localhost:9090"
    echo
    echo -e "${YELLOW}Default Credentials:${NC}"
    echo -e "  Admin Email: ${ADMIN_EMAIL:-<EMAIL>}"
    echo -e "  Admin Password: Check .env file"
    echo
    echo -e "${RED}IMPORTANT SECURITY NOTES:${NC}"
    echo -e "  1. Change all default passwords immediately"
    echo -e "  2. Replace self-signed SSL certificates with valid ones"
    echo -e "  3. Configure firewall rules"
    echo -e "  4. Set up proper backup procedures"
    echo -e "  5. Review and update security settings"
    echo
}

# Main setup function
main() {
    log "Starting Fortune 500 Admin Panel setup..."
    
    check_root
    check_requirements
    generate_secrets
    generate_ssl_certificates
    create_directories
    create_monitoring_config
    init_database
    start_services
    create_admin_user
    display_summary
    
    log "Setup completed! 🎉"
}

# Run main function
main "$@"
