/**
 * Fortune 500 Admin Panel JavaScript
 * Enterprise-grade client-side functionality with security focus
 */

class AdminPanel {
  constructor() {
    this.apiBase = '/api';
    this.currentUser = null;
    this.csrfToken = null;
    this.refreshTokenInterval = null;
    
    this.init();
  }

  async init() {
    try {
      // Get CSRF token
      await this.getCsrfToken();
      
      // Check if user is already authenticated
      const token = localStorage.getItem('adminToken');
      if (token) {
        await this.validateToken(token);
      } else {
        this.showLoginScreen();
      }
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Hide loading overlay
      this.hideLoading();
      
    } catch (error) {
      console.error('Admin panel initialization failed:', error);
      this.showLoginScreen();
      this.hideLoading();
    }
  }

  async getCsrfToken() {
    try {
      const response = await fetch('/api/csrf-token');
      const data = await response.json();
      this.csrfToken = data.csrfToken;
      
      // Update meta tag
      document.querySelector('meta[name="csrf-token"]').setAttribute('content', this.csrfToken);
    } catch (error) {
      console.error('Failed to get CSRF token:', error);
    }
  }

  async validateToken(token) {
    try {
      const response = await this.apiCall('/auth/me', 'GET', null, token);
      
      if (response.ok) {
        const data = await response.json();
        this.currentUser = data.user;
        this.showAdminInterface();
        this.startTokenRefresh();
        await this.loadDashboard();
      } else {
        localStorage.removeItem('adminToken');
        this.showLoginScreen();
      }
    } catch (error) {
      console.error('Token validation failed:', error);
      localStorage.removeItem('adminToken');
      this.showLoginScreen();
    }
  }

  setupEventListeners() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
      loginForm.addEventListener('submit', this.handleLogin.bind(this));
    }

    // Password toggle
    const togglePassword = document.getElementById('toggle-password');
    if (togglePassword) {
      togglePassword.addEventListener('click', this.togglePasswordVisibility);
    }

    // Logout
    const logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
      logoutLink.addEventListener('click', this.handleLogout.bind(this));
    }

    // Navigation
    const navLinks = document.querySelectorAll('[data-section]');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const section = link.getAttribute('data-section');
        this.navigateToSection(section);
      });
    });

    // Dashboard refresh
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', this.loadDashboard.bind(this));
    }

    // Global error handling
    window.addEventListener('unhandledrejection', this.handleGlobalError.bind(this));
    window.addEventListener('error', this.handleGlobalError.bind(this));
  }

  async handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const loginBtn = document.getElementById('login-btn');
    const errorDiv = document.getElementById('login-error');
    
    // Disable form
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Signing In...';
    errorDiv.classList.add('d-none');

    try {
      const loginData = {
        email: formData.get('email'),
        password: formData.get('password'),
        mfaToken: formData.get('mfaToken'),
        rememberMe: formData.get('rememberMe') === 'on'
      };

      const response = await this.apiCall('/auth/login', 'POST', loginData);
      const data = await response.json();

      if (response.ok) {
        if (data.requiresMFA) {
          // Show MFA section
          document.getElementById('mfa-section').classList.remove('d-none');
          document.getElementById('mfa-token').focus();
          
          // Store temporary token
          localStorage.setItem('tempToken', data.tempToken);
          
          this.showError('Please enter your MFA code to complete login.', 'info');
        } else {
          // Login successful
          localStorage.setItem('adminToken', data.accessToken);
          this.currentUser = data.user;
          
          this.showAdminInterface();
          this.startTokenRefresh();
          await this.loadDashboard();
          
          // Log successful login
          this.logSecurityEvent('login_success', {
            userId: data.user.id,
            mfaUsed: data.user.mfaEnabled
          });
        }
      } else {
        this.showError(data.error || 'Login failed');
        
        // Log failed login
        this.logSecurityEvent('login_failure', {
          email: loginData.email,
          reason: data.error
        });
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showError('Login failed. Please try again.');
    } finally {
      // Re-enable form
      loginBtn.disabled = false;
      loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right"></i> Sign In';
    }
  }

  async handleLogout() {
    try {
      const token = localStorage.getItem('adminToken');
      if (token) {
        await this.apiCall('/auth/logout', 'POST', {}, token);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('adminToken');
      localStorage.removeItem('tempToken');
      
      // Stop token refresh
      if (this.refreshTokenInterval) {
        clearInterval(this.refreshTokenInterval);
      }
      
      // Show login screen
      this.showLoginScreen();
      
      // Log logout
      this.logSecurityEvent('logout', {
        userId: this.currentUser?.id
      });
      
      this.currentUser = null;
    }
  }

  async apiCall(endpoint, method = 'GET', data = null, token = null) {
    const headers = {
      'Content-Type': 'application/json',
      'X-CSRF-Token': this.csrfToken
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    } else {
      const storedToken = localStorage.getItem('adminToken');
      if (storedToken) {
        headers['Authorization'] = `Bearer ${storedToken}`;
      }
    }

    const config = {
      method,
      headers,
      credentials: 'include'
    };

    if (data && method !== 'GET') {
      config.body = JSON.stringify(data);
    }

    const response = await fetch(`${this.apiBase}${endpoint}`, config);
    
    // Handle token expiration
    if (response.status === 401) {
      const errorData = await response.json();
      if (errorData.code === 'TOKEN_EXPIRED') {
        await this.refreshToken();
        // Retry the request
        return this.apiCall(endpoint, method, data);
      } else {
        // Invalid token, logout
        this.handleLogout();
      }
    }

    return response;
  }

  async refreshToken() {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': this.csrfToken
        }
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('adminToken', data.accessToken);
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      this.handleLogout();
    }
  }

  startTokenRefresh() {
    // Refresh token every 10 minutes
    this.refreshTokenInterval = setInterval(() => {
      this.refreshToken();
    }, 10 * 60 * 1000);
  }

  async loadDashboard() {
    try {
      // Load dashboard stats
      const [usersResponse, contentResponse, securityResponse, activityResponse] = await Promise.all([
        this.apiCall('/admin/stats/users'),
        this.apiCall('/admin/stats/content'),
        this.apiCall('/admin/stats/security'),
        this.apiCall('/admin/activity/recent')
      ]);

      // Update stats
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        document.getElementById('total-users').textContent = usersData.total;
      }

      if (contentResponse.ok) {
        const contentData = await contentResponse.json();
        document.getElementById('total-content').textContent = contentData.total;
      }

      if (securityResponse.ok) {
        const securityData = await securityResponse.json();
        document.getElementById('security-alerts').textContent = securityData.alerts;
        document.getElementById('active-sessions').textContent = securityData.activeSessions;
        document.getElementById('failed-logins').textContent = securityData.failedLogins;
        document.getElementById('active-threats').textContent = securityData.threats;
      }

      // Load recent activity
      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        this.renderRecentActivity(activityData.activities);
      }

      // Update user info
      this.updateUserInfo();

    } catch (error) {
      console.error('Dashboard loading error:', error);
    }
  }

  renderRecentActivity(activities) {
    const container = document.getElementById('recent-activity');
    
    if (!activities || activities.length === 0) {
      container.innerHTML = '<div class="text-center text-muted">No recent activity</div>';
      return;
    }

    const html = activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon bg-${this.getActivityColor(activity.eventType)}">
          <i class="bi bi-${this.getActivityIcon(activity.eventType)}"></i>
        </div>
        <div class="activity-content">
          <div class="activity-title">${activity.description}</div>
          <div class="activity-description">
            ${activity.username || 'System'} • ${activity.ipAddress}
          </div>
        </div>
        <div class="activity-time">
          ${this.formatTime(activity.timestamp)}
        </div>
      </div>
    `).join('');

    container.innerHTML = html;
  }

  getActivityColor(eventType) {
    const colors = {
      'login': 'success',
      'logout': 'secondary',
      'login_failed': 'danger',
      'user_created': 'primary',
      'content_updated': 'info',
      'security_violation': 'danger',
      'default': 'secondary'
    };
    return colors[eventType] || colors.default;
  }

  getActivityIcon(eventType) {
    const icons = {
      'login': 'box-arrow-in-right',
      'logout': 'box-arrow-right',
      'login_failed': 'shield-exclamation',
      'user_created': 'person-plus',
      'content_updated': 'file-text',
      'security_violation': 'shield-x',
      'default': 'activity'
    };
    return icons[eventType] || icons.default;
  }

  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  }

  updateUserInfo() {
    if (this.currentUser) {
      const userName = document.getElementById('user-name');
      if (userName) {
        userName.textContent = `${this.currentUser.firstName} ${this.currentUser.lastName}`;
      }
    }
  }

  navigateToSection(section) {
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
      link.classList.remove('active');
    });
    document.querySelector(`[data-section="${section}"]`).classList.add('active');

    // Show section
    document.querySelectorAll('.content-section').forEach(sec => {
      sec.classList.remove('active');
    });
    document.getElementById(`${section}-section`).classList.add('active');

    // Load section content if needed
    this.loadSectionContent(section);
  }

  async loadSectionContent(section) {
    // This would load specific content for each section
    // For now, just log the navigation
    console.log(`Loading section: ${section}`);
  }

  showLoginScreen() {
    document.getElementById('login-screen').classList.remove('d-none');
    document.getElementById('admin-interface').classList.add('d-none');
  }

  showAdminInterface() {
    document.getElementById('login-screen').classList.add('d-none');
    document.getElementById('admin-interface').classList.remove('d-none');
  }

  hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.style.opacity = '0';
      setTimeout(() => overlay.remove(), 300);
    }
  }

  showError(message, type = 'danger') {
    const errorDiv = document.getElementById('login-error');
    if (errorDiv) {
      errorDiv.className = `alert alert-${type}`;
      errorDiv.textContent = message;
      errorDiv.classList.remove('d-none');
    }
  }

  togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.getElementById('toggle-password');
    
    if (passwordInput.type === 'password') {
      passwordInput.type = 'text';
      toggleBtn.innerHTML = '<i class="bi bi-eye-slash"></i>';
    } else {
      passwordInput.type = 'password';
      toggleBtn.innerHTML = '<i class="bi bi-eye"></i>';
    }
  }

  logSecurityEvent(eventType, data) {
    // Log security events for monitoring
    console.log('Security Event:', { eventType, data, timestamp: new Date().toISOString() });
    
    // In production, this would send to a security monitoring service
    if (window.gtag) {
      window.gtag('event', eventType, {
        event_category: 'security',
        event_label: eventType,
        custom_map: data
      });
    }
  }

  handleGlobalError(event) {
    console.error('Global error:', event);
    
    // Log error for monitoring
    this.logSecurityEvent('client_error', {
      error: event.error?.message || event.reason?.message || 'Unknown error',
      stack: event.error?.stack || event.reason?.stack,
      url: window.location.href
    });
  }
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.adminPanel = new AdminPanel();
});

// Security: Clear sensitive data on page unload
window.addEventListener('beforeunload', () => {
  // Clear any sensitive data from memory
  if (window.adminPanel) {
    window.adminPanel.csrfToken = null;
  }
});
